import 'package:flutter/material.dart';

String a = "Disarm";
Color b = const Color.fromARGB(255, 13, 255, 0);
void changestatus() {
  if (a == "Disarmed") {
    a = "Armed";
    b = const Color.fromARGB(255, 13, 255, 0);
    // Status changed to armed
  } else {
    a = "Disarmed";
    b = const Color.fromARGB(255, 255, 0, 0);
    // Status changed to disarmed
  }
}

void main() {
  runApp(MaterialApp(home: Sandbox()));
}

class Sandbox extends StatelessWidget {
  const Sandbox({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'AMAN',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.black,

        actions: [
          IconButton(
            onPressed: () {},
            icon: Icon(Icons.notifications, color: Colors.white),
          ),
          IconButton(
            onPressed: () {},
            icon: Icon(Icons.person, color: Colors.white),
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(0, 20, 0, 20),
            color: Colors.black,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "System staus",
                      style: TextStyle(
                        color: const Color.fromARGB(255, 255, 255, 255),
                        fontWeight: FontWeight.w400,
                        fontSize: 20,
                      ),
                    ),
                    Text(
                      a,
                      style: TextStyle(
                        color: b,
                        fontWeight: FontWeight.bold,
                        fontSize: 30,
                        fontStyle: FontStyle.normal,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Icon(Icons.circle, color: Colors.green, size: 15),

                    Text(
                      "Home mode",
                      style: TextStyle(
                        color: const Color.fromARGB(255, 255, 255, 255),
                        fontWeight: FontWeight.w400,
                        fontSize: 15,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            alignment: Alignment(0, 0),
            padding: EdgeInsets.fromLTRB(0, 20, 0, 20),
            color: Colors.black,
            child: Text(
              "All zones secure * last checked 2 min ago",
              style: TextStyle(
                color: const Color.fromARGB(255, 255, 255, 255),
                fontWeight: FontWeight.w600,
                fontSize: 15,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(5, 40, 5, 40),
            color: Colors.black,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                OutlinedButton(
                  onPressed: changestatus,
                  style: OutlinedButton.styleFrom(
                    fixedSize: Size(150, 60),
                    iconColor: const Color.fromARGB(255, 253, 253, 253),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadiusGeometry.circular(10),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.lock_open),
                      Text("  Disarm", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                ),
                OutlinedButton(
                  onPressed: () {},
                  style: OutlinedButton.styleFrom(
                    fixedSize: Size(145, 60),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadiusGeometry.circular(10),
                    ),
                    iconColor: const Color.fromARGB(255, 253, 253, 253),
                    backgroundColor: Colors.red,
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.dangerous_outlined),
                      Text("Emergency", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(0, 20, 0, 20),
            color: Colors.black,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    Icon(Icons.camera_alt, color: Colors.white),
                    Text("camera", style: TextStyle(color: Colors.white)),
                  ],
                ),
                Column(
                  children: [
                    Icon(Icons.lock, color: Colors.white),
                    Text("locks", style: TextStyle(color: Colors.white)),
                  ],
                ),
                Column(
                  children: [
                    Icon(Icons.sensor_door, color: Colors.white),
                    Text("sensors", style: TextStyle(color: Colors.white)),
                  ],
                ),
                Column(
                  children: [
                    Icon(Icons.verified_user, color: Colors.white),
                    Text("users", style: TextStyle(color: Colors.white)),
                  ],
                ),
              ],
            ),
          ),
          Container(
            height: 291,
            color: Colors.black,

            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,

              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Recent Activity",
                      style: TextStyle(color: Colors.white, fontSize: 20),
                    ),
                    TextButton(onPressed: () {}, child: Text("view all")),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Icon(
                      Icons.door_sliding_rounded,
                      color: const Color.fromARGB(255, 243, 4, 4),
                    ),
                    Column(
                      children: [
                        Text(
                          "front door oppend",
                          style: TextStyle(color: Colors.white),
                        ),
                        Text(
                          "oppened at 11:05 pm",
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                    Text("3m ago", style: TextStyle(color: Colors.white)),
                  ],
                ),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,

                    children: [
                      Icon(
                        Icons.door_sliding_rounded,
                        color: const Color.fromARGB(255, 255, 247, 0),
                      ),
                      Column(
                        children: [
                          Text(
                            "motion detected",
                            style: TextStyle(color: Colors.white),
                          ),
                          Text(
                            "backyard camera",
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                      Text("3m ago", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Icon(
                      Icons.shield,
                      color: const Color.fromARGB(255, 104, 248, 1),
                    ),
                    Column(
                      children: [
                        Text(
                          "System Armed",
                          style: TextStyle(color: Colors.white),
                        ),
                        Text(
                          "home mode activated",
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                    Text("3m ago", style: TextStyle(color: Colors.white)),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
